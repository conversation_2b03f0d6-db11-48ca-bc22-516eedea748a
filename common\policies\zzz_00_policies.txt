orbital_bombardment = {

	potential = {
		OR = {
			is_country_type = default
			is_country_type = vol
		}
	}

	option = {
		name = "orbital_bombardment_selective"
		policy_flags = {
			orbital_bombardment_selective
		}

		AI_weight = {
			modifier = {
				factor = 0
				NOR = {
					has_ethic = "ethic_pacifist"
					has_ethic = "ethic_fanatic_pacifist"
				}
				NOT = {
					is_galactic_community_member = yes
					OR = {
						is_active_resolution = "resolution_rulesofwar_independent_tribunals"
						is_active_resolution = "resolution_rulesofwar_last_resort_doctrine"
						is_active_resolution = "resolution_rulesofwar_demobilization_initiative"
					}
				}
			}
		}
	}
	option = {
		name = "orbital_bombardment_indiscriminate"

		in_breach_of = {
			{
				key = resolution_rulesofwar_independent_tribunals
			}
			{
				key = resolution_rulesofwar_last_resort_doctrine
			}
			{
				key = resolution_rulesofwar_demobilization_initiative
			}
		}

		policy_flags = {
			orbital_bombardment_indiscriminate
		}
		modifier = {}

		valid = {
			NOR = {
				has_ethic = "ethic_pacifist"
				has_ethic = "ethic_fanatic_pacifist"
			}
		}
		AI_weight = {
			modifier = {
				factor = 0.1
				is_galactic_community_member = yes
				OR = {
					is_active_resolution = "resolution_rulesofwar_independent_tribunals"
					is_active_resolution = "resolution_rulesofwar_last_resort_doctrine"
					is_active_resolution = "resolution_rulesofwar_demobilization_initiative"
				}
			}
		}
	}
	option = {
		name = "orbital_bombardment_armageddon"

		in_breach_of = {
			{
				key = resolution_rulesofwar_independent_tribunals
			}
			{
				key = resolution_rulesofwar_last_resort_doctrine
			}
			{
				key = resolution_rulesofwar_demobilization_initiative
			}
		}

		potential = {
			OR = {
				has_valid_civic = civic_fanatic_purifiers
				has_valid_civic = civic_machine_terminator
				has_purity_imperfection_remediation_tradition = yes
			}
		}

		policy_flags = {
			orbital_bombardment_armageddon
		}
		modifier = {}

		AI_weight = {
			modifier = {
				factor = 10
				OR = {
					has_valid_civic = civic_fanatic_purifiers
					has_valid_civic = civic_machine_terminator
				}
			}
			modifier = {
				factor = 10
				has_ascension_perk = ap_become_the_crisis
			}
		}
	}
}